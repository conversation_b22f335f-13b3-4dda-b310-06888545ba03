import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Music,
  Zap,
  Download,
  Repeat,
  Waves,
  Palette,
  Clock,
  Shield,
  Headphones,
  Sparkles,
  Settings,
  CheckCircle
} from "lucide-react";

const FEATURES = [
  {
    icon: Zap,
    title: "Lightning Fast Generation",
    description: "Create professional music loops in under 30 seconds with our advanced AI technology.",
    badge: "Fast",
    color: "text-yellow-500",
  },
  {
    icon: Repeat,
    title: "Perfect Seamless Loops",
    description: "Every track is automatically verified for seamless looping with 95%+ accuracy.",
    badge: "Quality",
    color: "text-green-500",
  },
  {
    icon: Palette,
    title: "Multiple Music Styles",
    description: "From ambient to electronic, jazz to rock - generate music in any style you need.",
    badge: "Variety",
    color: "text-purple-500",
  },
  {
    icon: Waves,
    title: "Visual Waveform Editor",
    description: "See your music with interactive waveforms and precise loop point visualization.",
    badge: "Visual",
    color: "text-blue-500",
  },
  {
    icon: Settings,
    title: "Advanced Controls",
    description: "Fine-tune BPM, mood, style, and duration to get exactly the sound you want.",
    badge: "Control",
    color: "text-orange-500",
  },
  {
    icon: Download,
    title: "Multiple Export Formats",
    description: "Download in MP3 or WAV format with commercial use licensing included.",
    badge: "Export",
    color: "text-indigo-500",
  },
];

const WORKFLOW_STEPS = [
  {
    step: 1,
    title: "Describe Your Music",
    description: "Tell our AI what kind of music you want using natural language.",
    icon: Music,
  },
  {
    step: 2,
    title: "AI Generates Your Loop",
    description: "Our advanced AI creates a unique, seamless music loop in seconds.",
    icon: Sparkles,
  },
  {
    step: 3,
    title: "Preview & Download",
    description: "Listen to your track, verify the loop quality, and download instantly.",
    icon: Headphones,
  },
];

const QUALITY_FEATURES = [
  "Seamless loop verification",
  "Professional audio quality",
  "Commercial use license",
  "Multiple duration options",
  "BPM control and detection",
  "Style and mood customization",
];

export default function MusicFeatures() {
  return (
    <section id="features" className="py-24 bg-muted/30">
      <div className="container">
        {/* Main features grid */}
        <div className="text-center mb-16">
          <Badge variant="secondary" className="mb-4">
            Features
          </Badge>
          <h2 className="text-3xl font-bold mb-4">
            Everything You Need for Music Creation
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Professional-grade music generation tools designed for creators, 
            developers, and content makers.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-24">
          {FEATURES.map((feature, index) => (
            <Card key={index} className="relative overflow-hidden group hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center gap-3 mb-2">
                  <div className={`p-2 rounded-lg bg-background ${feature.color}`}>
                    <feature.icon className="h-5 w-5" />
                  </div>
                  <Badge variant="outline" className="text-xs">
                    {feature.badge}
                  </Badge>
                </div>
                <CardTitle className="text-lg">{feature.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-sm leading-relaxed">
                  {feature.description}
                </CardDescription>
              </CardContent>
              
              {/* Hover effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-secondary/5 opacity-0 group-hover:opacity-100 transition-opacity" />
            </Card>
          ))}
        </div>

        {/* How it works */}
        <div className="mb-24">
          <div className="text-center mb-12">
            <Badge variant="secondary" className="mb-4">
              How It Works
            </Badge>
            <h2 className="text-3xl font-bold mb-4">
              Create Music in 3 Simple Steps
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Our streamlined workflow makes music creation accessible to everyone.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {WORKFLOW_STEPS.map((step, index) => (
              <div key={index} className="text-center relative">
                {/* Step number */}
                <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-primary text-primary-foreground font-bold text-lg mb-4">
                  {step.step}
                </div>
                
                {/* Icon */}
                <div className="flex justify-center mb-4">
                  <div className="p-3 rounded-lg bg-primary/10">
                    <step.icon className="h-6 w-6 text-primary" />
                  </div>
                </div>

                {/* Content */}
                <h3 className="text-xl font-semibold mb-2">{step.title}</h3>
                <p className="text-muted-foreground">{step.description}</p>

                {/* Connector line */}
                {index < WORKFLOW_STEPS.length - 1 && (
                  <div className="hidden md:block absolute top-6 left-1/2 w-full h-0.5 bg-border transform translate-x-6" />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Quality assurance */}
        <div className="bg-background rounded-2xl p-8 lg:p-12">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <Badge variant="secondary" className="mb-4">
                Quality Assurance
              </Badge>
              <h2 className="text-3xl font-bold mb-4">
                Professional Quality, Every Time
              </h2>
              <p className="text-lg text-muted-foreground mb-6">
                Our AI is trained on professional music libraries and includes 
                advanced quality verification to ensure every loop meets industry standards.
              </p>
              
              <div className="space-y-3">
                {QUALITY_FEATURES.map((feature, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <CheckCircle className="h-5 w-5 text-green-500 shrink-0" />
                    <span className="text-sm">{feature}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="relative">
              {/* Placeholder for quality visualization */}
              <div className="aspect-square bg-gradient-to-br from-primary/20 to-secondary/20 rounded-2xl flex items-center justify-center">
                <div className="text-center">
                  <div className="text-4xl font-bold text-primary mb-2">95%</div>
                  <div className="text-sm text-muted-foreground">Loop Quality Score</div>
                  <div className="mt-4 flex justify-center">
                    <div className="flex items-center gap-1">
                      {Array.from({ length: 5 }).map((_, i) => (
                        <div
                          key={i}
                          className="w-2 h-8 bg-primary/60 rounded-sm"
                          style={{ height: `${Math.random() * 32 + 16}px` }}
                        />
                      ))}
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Floating quality badges */}
              <div className="absolute -top-4 -right-4 bg-background border rounded-lg p-3 shadow-lg">
                <div className="flex items-center gap-2 text-sm">
                  <Shield className="h-4 w-4 text-green-500" />
                  <span className="font-medium">Verified Loop</span>
                </div>
              </div>
              
              <div className="absolute -bottom-4 -left-4 bg-background border rounded-lg p-3 shadow-lg">
                <div className="flex items-center gap-2 text-sm">
                  <Clock className="h-4 w-4 text-blue-500" />
                  <span className="font-medium">30s Generation</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
